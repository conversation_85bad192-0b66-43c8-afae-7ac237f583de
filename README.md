# RBAC 后台管理系统

基于角色的访问控制（RBAC）后台管理系统，采用前后端分离架构。

## 技术栈

### 前端
- React 18
- AWS Cloudscape Design System
- React Router v6
- Axios
- React Context API

### 后端
- Python 3.9+
- FastAPI
- SQLAlchemy
- Pydantic
- Passlib
- JWT

### 数据库
- SQLite

## 项目结构

```
zengzhang-v2-dev04/
├── backend/                 # 后端 FastAPI 应用
│   ├── app/
│   │   ├── api/            # API 路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── schemas/        # Pydantic 模式
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
│   ├── tests/              # 测试文件
│   ├── requirements.txt    # Python 依赖
│   └── main.py            # 应用入口
├── frontend/               # 前端 React 应用
│   ├── public/
│   ├── src/
│   │   ├── components/     # 可复用组件
│   │   ├── pages/          # 页面组件
│   │   ├── contexts/       # React Context
│   │   ├── services/       # API 服务
│   │   ├── utils/          # 工具函数
│   │   └── types/          # TypeScript 类型
│   ├── package.json
│   └── vite.config.ts
├── docs/                   # 文档
└── README.md
```

## 快速开始

### 一键启动（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd zengzhang-v2-dev04

# 运行启动脚本
./start.sh
```

启动脚本会自动完成所有配置和启动过程。

### 手动启动

#### 后端启动

```bash
cd backend
pip install -r requirements.txt
PYTHONPATH=$(pwd) python app/initial_data.py  # 初始化数据库
PYTHONPATH=$(pwd) uvicorn main:app --reload
```

#### 前端启动

```bash
cd frontend
npm install
npm run dev
```

### 停止服务

```bash
./stop.sh
```

## 功能特性

- ✅ 用户管理：创建、编辑、删除用户，支持搜索和分页
- ✅ 角色管理：定义和管理系统角色，灵活的权限分配
- ✅ 权限管理：细粒度权限控制，基于资源和操作的权限模型
- ✅ 基于 JWT 的认证系统，安全可靠
- ✅ 响应式设计界面，支持多设备访问
- ✅ 完整的 RBAC 权限模型
- ✅ 实时权限验证和路由保护
- ✅ 现代化的 UI 组件（AWS Cloudscape）
- ✅ RESTful API 设计
- ✅ 完整的错误处理和用户反馈

## 系统截图

### 登录页面
- 简洁的登录界面
- 默认管理员账户：admin / admin123

### 仪表板
- 系统统计信息
- 用户信息展示
- 快速操作入口

### 用户管理
- 用户列表展示
- 搜索和分页功能
- 创建、编辑、删除用户
- 角色分配管理

## 访问地址

启动成功后，可以通过以下地址访问：

- **前端应用**: http://localhost:5173
- **后端 API**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs
- **ReDoc 文档**: http://localhost:8000/redoc

## 默认账户

- **用户名**: admin
- **密码**: admin123
- **角色**: 超级管理员（拥有所有权限）

## 文档

- [开发指南](docs/development.md) - 开发环境设置和代码规范
- [部署指南](docs/deployment.md) - 生产环境部署说明
- [用户手册](docs/user-manual.md) - 系统使用说明
- [API 文档](docs/api.md) - 完整的 API 接口文档

## 技术支持

如遇到问题，请：
1. 查看相关文档
2. 检查日志文件
3. 提交 Issue

## 许可证

MIT License
