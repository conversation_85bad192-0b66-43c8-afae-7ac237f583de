#!/bin/bash

# RBAC 后台管理系统启动脚本

echo "🚀 启动 RBAC 后台管理系统"
echo "================================"

# 检查是否安装了必要的依赖
echo "📦 检查依赖..."

# 检查 Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 未安装"
    exit 1
fi

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装"
    exit 1
fi

# 检查 npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装"
    exit 1
fi

echo "✅ 依赖检查完成"

# 安装后端依赖
echo "📦 安装后端依赖..."
cd backend
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
fi

source venv/bin/activate
pip install -r requirements.txt

# 初始化数据库
echo "🗄️ 初始化数据库..."
PYTHONPATH=$(pwd) python app/initial_data.py

echo "✅ 后端设置完成"

# 安装前端依赖
echo "📦 安装前端依赖..."
cd ../frontend
npm install

echo "✅ 前端设置完成"

# 启动服务
echo "🚀 启动服务..."

# 启动后端服务（后台运行）
echo "启动后端服务 (http://localhost:8000)..."
cd ../backend
PYTHONPATH=$(pwd) nohup uvicorn main:app --reload --host 0.0.0.0 --port 8000 > backend.log 2>&1 &
BACKEND_PID=$!

# 等待后端启动
sleep 3

# 启动前端服务（后台运行）
echo "启动前端服务 (http://localhost:5173)..."
cd ../frontend
nohup npm run dev > frontend.log 2>&1 &
FRONTEND_PID=$!

# 等待前端启动
sleep 3

echo "================================"
echo "🎉 系统启动完成！"
echo ""
echo "📍 访问地址:"
echo "   前端: http://localhost:5173"
echo "   后端 API: http://localhost:8000"
echo "   API 文档: http://localhost:8000/docs"
echo ""
echo "👤 默认管理员账户:"
echo "   用户名: admin"
echo "   密码: admin123"
echo ""
echo "🔧 进程 ID:"
echo "   后端: $BACKEND_PID"
echo "   前端: $FRONTEND_PID"
echo ""
echo "📝 日志文件:"
echo "   后端: backend/backend.log"
echo "   前端: frontend/frontend.log"
echo ""
echo "⏹️ 停止服务:"
echo "   kill $BACKEND_PID $FRONTEND_PID"
echo ""
echo "🌐 正在打开浏览器..."

# 等待服务完全启动
sleep 2

# 打开浏览器
if command -v open &> /dev/null; then
    open http://localhost:5173
elif command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:5173
else
    echo "请手动打开浏览器访问 http://localhost:5173"
fi

echo "✨ 享受使用 RBAC 后台管理系统！"
