#!/usr/bin/env python3
"""
API 测试脚本
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_login():
    """测试登录功能"""
    print("测试登录功能...")
    
    # 测试登录
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    
    if response.status_code == 200:
        data = response.json()
        print("✓ 登录成功")
        print(f"  用户: {data['user']['username']}")
        print(f"  角色: {[role['name'] for role in data['user']['roles']]}")
        return data['access_token']
    else:
        print("✗ 登录失败")
        print(f"  错误: {response.text}")
        return None

def test_users_api(token):
    """测试用户管理 API"""
    print("\n测试用户管理 API...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 获取用户列表
    response = requests.get(f"{BASE_URL}/users", headers=headers)
    if response.status_code == 200:
        data = response.json()
        print(f"✓ 获取用户列表成功，共 {data['total']} 个用户")
    else:
        print("✗ 获取用户列表失败")
        print(f"  错误: {response.text}")
        return
    
    # 创建测试用户
    new_user = {
        "username": "testuser",
        "email": "<EMAIL>",
        "full_name": "测试用户",
        "password": "password123",
        "is_active": True
    }
    
    response = requests.post(f"{BASE_URL}/users", json=new_user, headers=headers)
    if response.status_code == 200:
        user_data = response.json()
        print(f"✓ 创建用户成功，用户ID: {user_data['id']}")
        
        # 删除测试用户
        response = requests.delete(f"{BASE_URL}/users/{user_data['id']}", headers=headers)
        if response.status_code == 200:
            print("✓ 删除测试用户成功")
        else:
            print("✗ 删除测试用户失败")
    else:
        print("✗ 创建用户失败")
        print(f"  错误: {response.text}")

def test_roles_api(token):
    """测试角色管理 API"""
    print("\n测试角色管理 API...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 获取角色列表
    response = requests.get(f"{BASE_URL}/roles", headers=headers)
    if response.status_code == 200:
        data = response.json()
        print(f"✓ 获取角色列表成功，共 {data['total']} 个角色")
    else:
        print("✗ 获取角色列表失败")
        print(f"  错误: {response.text}")

def test_permissions_api(token):
    """测试权限管理 API"""
    print("\n测试权限管理 API...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 获取权限列表
    response = requests.get(f"{BASE_URL}/permissions", headers=headers)
    if response.status_code == 200:
        data = response.json()
        print(f"✓ 获取权限列表成功，共 {data['total']} 个权限")
    else:
        print("✗ 获取权限列表失败")
        print(f"  错误: {response.text}")

def test_auth_api(token):
    """测试认证 API"""
    print("\n测试认证 API...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 获取当前用户信息
    response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
    if response.status_code == 200:
        data = response.json()
        print(f"✓ 获取当前用户信息成功: {data['username']}")
    else:
        print("✗ 获取当前用户信息失败")
        print(f"  错误: {response.text}")
    
    # 获取用户权限
    response = requests.get(f"{BASE_URL}/auth/permissions", headers=headers)
    if response.status_code == 200:
        data = response.json()
        print(f"✓ 获取用户权限成功，共 {len(data['permissions'])} 个权限")
    else:
        print("✗ 获取用户权限失败")
        print(f"  错误: {response.text}")

def main():
    """主测试函数"""
    print("开始 API 测试...")
    print("=" * 50)
    
    # 测试登录
    token = test_login()
    if not token:
        print("登录失败，无法继续测试")
        return
    
    # 测试各个 API
    test_auth_api(token)
    test_users_api(token)
    test_roles_api(token)
    test_permissions_api(token)
    
    print("\n" + "=" * 50)
    print("API 测试完成")

if __name__ == "__main__":
    main()
