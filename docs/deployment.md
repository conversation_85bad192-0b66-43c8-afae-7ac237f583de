# 部署指南

## 系统要求

### 最低要求
- Python 3.9+
- Node.js 18+
- npm 或 yarn
- 2GB RAM
- 10GB 磁盘空间

### 推荐配置
- Python 3.11+
- Node.js 20+
- 4GB RAM
- 20GB 磁盘空间

## 快速部署

### 1. 使用启动脚本（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd zengzhang-v2-dev04

# 运行启动脚本
./start.sh
```

启动脚本会自动：
- 检查依赖
- 安装后端和前端依赖
- 初始化数据库
- 启动后端和前端服务
- 打开浏览器

### 2. 手动部署

#### 后端部署

```bash
cd backend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置必要的配置

# 初始化数据库
PYTHONPATH=$(pwd) python app/initial_data.py

# 启动服务
PYTHONPATH=$(pwd) uvicorn main:app --host 0.0.0.0 --port 8000
```

#### 前端部署

```bash
cd frontend

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置 API 地址

# 启动开发服务器
npm run dev

# 或构建生产版本
npm run build
```

## 生产环境部署

### 后端生产部署

1. **使用 Gunicorn**

```bash
# 安装 Gunicorn
pip install gunicorn

# 启动服务
gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app --bind 0.0.0.0:8000
```

2. **使用 Docker**

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["gunicorn", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "main:app", "--bind", "0.0.0.0:8000"]
```

### 前端生产部署

1. **构建静态文件**

```bash
npm run build
```

2. **使用 Nginx 部署**

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    root /path/to/frontend/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 环境配置

### 后端环境变量

```bash
# 数据库配置
DATABASE_URL=sqlite:///./rbac_system.db

# JWT 配置
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
APP_NAME=RBAC Management System
DEBUG=False
API_V1_STR=/api/v1

# CORS 配置
BACKEND_CORS_ORIGINS=["https://your-domain.com"]
```

### 前端环境变量

```bash
# API 配置
VITE_API_BASE_URL=https://api.your-domain.com/api/v1

# 应用配置
VITE_APP_NAME=RBAC Management System
VITE_APP_VERSION=1.0.0
```

## 数据库配置

### SQLite（默认）
- 适用于小型应用和开发环境
- 数据文件：`rbac_system.db`

### PostgreSQL（推荐生产环境）

```bash
# 安装 PostgreSQL 驱动
pip install psycopg2-binary

# 更新 DATABASE_URL
DATABASE_URL=postgresql://username:password@localhost/rbac_system
```

### MySQL

```bash
# 安装 MySQL 驱动
pip install pymysql

# 更新 DATABASE_URL
DATABASE_URL=mysql+pymysql://username:password@localhost/rbac_system
```

## 监控和日志

### 应用监控
- 使用 Prometheus + Grafana
- 监控 API 响应时间、错误率
- 监控系统资源使用情况

### 日志管理
- 配置结构化日志
- 使用 ELK Stack 或类似工具
- 设置日志轮转

## 安全配置

### HTTPS 配置
- 使用 Let's Encrypt 获取 SSL 证书
- 配置 HTTPS 重定向
- 设置安全头

### 防火墙配置
- 只开放必要端口（80, 443）
- 限制数据库访问
- 配置 fail2ban

## 备份策略

### 数据库备份
```bash
# SQLite 备份
cp rbac_system.db rbac_system_backup_$(date +%Y%m%d).db

# PostgreSQL 备份
pg_dump rbac_system > rbac_system_backup_$(date +%Y%m%d).sql
```

### 自动备份脚本
```bash
#!/bin/bash
# backup.sh
BACKUP_DIR="/path/to/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
cp rbac_system.db $BACKUP_DIR/rbac_system_$DATE.db

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "rbac_system_*.db" -mtime +30 -delete
```

## 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查找占用端口的进程
lsof -i :8000
lsof -i :5173

# 杀死进程
kill -9 <PID>
```

2. **权限问题**
```bash
# 检查文件权限
ls -la rbac_system.db

# 修改权限
chmod 644 rbac_system.db
```

3. **依赖问题**
```bash
# 重新安装依赖
pip install --force-reinstall -r requirements.txt
npm ci
```

### 日志查看
```bash
# 后端日志
tail -f backend.log

# 前端日志
tail -f frontend.log

# 系统日志
journalctl -u your-service-name -f
```
