# 用户使用手册

## 系统概述

RBAC 后台管理系统是一个基于角色的访问控制管理平台，提供用户、角色、权限的统一管理功能。系统采用现代化的 Web 界面，支持细粒度的权限控制。

## 系统访问

### 登录系统

1. 打开浏览器，访问系统地址
2. 在登录页面输入用户名和密码
3. 点击"登录"按钮

**默认管理员账户：**
- 用户名：`admin`
- 密码：`admin123`

### 退出系统

1. 点击右上角用户菜单
2. 选择"退出登录"

## 主要功能

### 仪表板

仪表板是系统的首页，显示：
- 系统统计信息（用户数、角色数、权限数等）
- 当前用户信息
- 用户角色信息
- 快速操作链接

### 用户管理

#### 查看用户列表

1. 在左侧导航菜单点击"用户管理"
2. 系统显示用户列表，包含：
   - 用户名
   - 邮箱
   - 姓名
   - 状态（活跃/禁用）
   - 分配的角色
   - 创建时间

#### 搜索用户

1. 在用户列表页面的搜索框中输入关键词
2. 系统会实时搜索用户名、邮箱、姓名匹配的用户

#### 创建用户

1. 在用户管理页面点击"创建用户"按钮
2. 填写用户信息：
   - **用户名**：3-50 字符，系统唯一
   - **邮箱**：有效的邮箱地址，系统唯一
   - **姓名**：用户的真实姓名
   - **密码**：至少 6 个字符
   - **状态**：选择活跃或禁用
3. 点击"创建"按钮保存

#### 编辑用户

1. 在用户列表中找到要编辑的用户
2. 点击"编辑"按钮
3. 修改用户信息
4. 点击"保存"按钮

#### 删除用户

1. 在用户列表中找到要删除的用户
2. 点击"删除"按钮
3. 确认删除操作

**注意：**
- 不能删除自己的账户
- 删除用户会同时移除其所有角色分配

#### 分配角色

1. 在用户详情页面或编辑页面
2. 选择要分配的角色
3. 点击"分配角色"按钮

### 角色管理

#### 查看角色列表

1. 在左侧导航菜单点击"角色管理"
2. 系统显示角色列表，包含：
   - 角色名称
   - 描述
   - 分配的权限
   - 创建时间

#### 创建角色

1. 在角色管理页面点击"创建角色"按钮
2. 填写角色信息：
   - **角色名称**：唯一的角色标识
   - **描述**：角色的详细说明
3. 点击"创建"按钮保存

#### 编辑角色

1. 在角色列表中找到要编辑的角色
2. 点击"编辑"按钮
3. 修改角色信息
4. 点击"保存"按钮

#### 删除角色

1. 在角色列表中找到要删除的角色
2. 点击"删除"按钮
3. 确认删除操作

**注意：**
- 不能删除已分配给用户的角色
- 需要先移除角色的所有用户分配

#### 分配权限

1. 在角色详情页面或编辑页面
2. 选择要分配的权限
3. 点击"分配权限"按钮

### 权限管理

#### 查看权限列表

1. 在左侧导航菜单点击"权限管理"
2. 系统显示权限列表，包含：
   - 权限名称
   - 资源
   - 操作
   - 描述
   - 创建时间

#### 创建权限

1. 在权限管理页面点击"创建权限"按钮
2. 填写权限信息：
   - **权限名称**：唯一的权限标识
   - **资源**：权限控制的资源（如 users, roles）
   - **操作**：允许的操作（如 read, create, update, delete）
   - **描述**：权限的详细说明
3. 点击"创建"按钮保存

#### 编辑权限

1. 在权限列表中找到要编辑的权限
2. 点击"编辑"按钮
3. 修改权限信息
4. 点击"保存"按钮

#### 删除权限

1. 在权限列表中找到要删除的权限
2. 点击"删除"按钮
3. 确认删除操作

**注意：**
- 不能删除已分配给角色的权限
- 需要先移除权限的所有角色分配

## 权限说明

### 权限模型

系统采用基于资源和操作的权限模型：

- **资源（Resource）**：系统中的功能模块，如 users、roles、permissions
- **操作（Action）**：对资源的具体操作，如 read、create、update、delete

### 内置权限

系统预置以下权限：

#### 用户管理权限
- `users:read` - 查看用户信息
- `users:create` - 创建新用户
- `users:update` - 更新用户信息
- `users:delete` - 删除用户

#### 角色管理权限
- `roles:read` - 查看角色信息
- `roles:create` - 创建新角色
- `roles:update` - 更新角色信息
- `roles:delete` - 删除角色

#### 权限管理权限
- `permissions:read` - 查看权限信息
- `permissions:create` - 创建新权限
- `permissions:update` - 更新权限信息
- `permissions:delete` - 删除权限

#### 仪表板权限
- `dashboard:read` - 查看仪表板

### 内置角色

#### 超级管理员
- 拥有系统所有权限
- 可以管理用户、角色、权限
- 默认分配给 admin 用户

#### 普通用户
- 只能查看仪表板
- 适用于一般用户

## 常见问题

### Q: 忘记密码怎么办？
A: 请联系系统管理员重置密码。

### Q: 为什么看不到某些菜单？
A: 这是由于权限限制，请联系管理员分配相应权限。

### Q: 如何修改个人信息？
A: 目前需要联系管理员修改，后续版本会支持用户自助修改。

### Q: 系统支持多少用户？
A: 系统理论上支持无限用户，实际限制取决于服务器配置。

## 技术支持

如遇到技术问题，请联系系统管理员或查看：
- 开发文档：`docs/development.md`
- 部署文档：`docs/deployment.md`
- 项目 README：`README.md`
