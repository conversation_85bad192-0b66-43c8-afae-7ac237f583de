# API 文档

## 概述

RBAC 后台管理系统提供 RESTful API，支持用户、角色、权限的完整管理功能。

**基础信息：**
- 基础 URL：`http://localhost:8000/api/v1`
- 认证方式：JWT Bearer Token
- 数据格式：JSON

## 认证

### 获取访问令牌

```http
POST /auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

**响应：**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "user": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "full_name": "系统管理员",
    "is_active": true,
    "created_at": "2025-01-01T00:00:00",
    "updated_at": "2025-01-01T00:00:00",
    "roles": [...]
  }
}
```

### 使用访问令牌

在请求头中添加：
```http
Authorization: Bearer <access_token>
```

## 用户管理 API

### 获取用户列表

```http
GET /users?page=1&size=10&search=keyword
Authorization: Bearer <token>
```

**查询参数：**
- `page`：页码（默认 1）
- `size`：每页数量（默认 10，最大 100）
- `search`：搜索关键词（可选）

**响应：**
```json
{
  "users": [...],
  "total": 100,
  "page": 1,
  "size": 10,
  "pages": 10
}
```

### 获取用户详情

```http
GET /users/{user_id}
Authorization: Bearer <token>
```

### 创建用户

```http
POST /users
Authorization: Bearer <token>
Content-Type: application/json

{
  "username": "newuser",
  "email": "<EMAIL>",
  "full_name": "新用户",
  "password": "password123",
  "is_active": true
}
```

### 更新用户

```http
PUT /users/{user_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "username": "updateduser",
  "email": "<EMAIL>",
  "full_name": "更新的用户",
  "is_active": false
}
```

### 删除用户

```http
DELETE /users/{user_id}
Authorization: Bearer <token>
```

### 分配角色给用户

```http
POST /users/{user_id}/roles
Authorization: Bearer <token>
Content-Type: application/json

{
  "role_ids": [1, 2, 3]
}
```

### 移除用户角色

```http
DELETE /users/{user_id}/roles/{role_id}
Authorization: Bearer <token>
```

## 角色管理 API

### 获取角色列表

```http
GET /roles?page=1&size=10&search=keyword
Authorization: Bearer <token>
```

### 获取角色详情

```http
GET /roles/{role_id}
Authorization: Bearer <token>
```

### 创建角色

```http
POST /roles
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "新角色",
  "description": "角色描述"
}
```

### 更新角色

```http
PUT /roles/{role_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "更新的角色",
  "description": "更新的描述"
}
```

### 删除角色

```http
DELETE /roles/{role_id}
Authorization: Bearer <token>
```

### 分配权限给角色

```http
POST /roles/{role_id}/permissions
Authorization: Bearer <token>
Content-Type: application/json

{
  "permission_ids": [1, 2, 3]
}
```

### 移除角色权限

```http
DELETE /roles/{role_id}/permissions/{permission_id}
Authorization: Bearer <token>
```

## 权限管理 API

### 获取权限列表

```http
GET /permissions?page=1&size=10&search=keyword
Authorization: Bearer <token>
```

### 获取权限详情

```http
GET /permissions/{permission_id}
Authorization: Bearer <token>
```

### 创建权限

```http
POST /permissions
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "新权限",
  "resource": "resource_name",
  "action": "action_name",
  "description": "权限描述"
}
```

### 更新权限

```http
PUT /permissions/{permission_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "更新的权限",
  "resource": "updated_resource",
  "action": "updated_action",
  "description": "更新的描述"
}
```

### 删除权限

```http
DELETE /permissions/{permission_id}
Authorization: Bearer <token>
```

### 根据资源获取权限

```http
GET /permissions/resource/{resource}
Authorization: Bearer <token>
```

## 认证 API

### 获取当前用户信息

```http
GET /auth/me
Authorization: Bearer <token>
```

### 获取当前用户权限

```http
GET /auth/permissions
Authorization: Bearer <token>
```

**响应：**
```json
{
  "permissions": [
    {
      "resource": "users",
      "action": "read",
      "name": "用户查看"
    },
    ...
  ]
}
```

### 用户登出

```http
POST /auth/logout
Authorization: Bearer <token>
```

## 错误处理

### HTTP 状态码

- `200` - 成功
- `201` - 创建成功
- `400` - 请求错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器错误

### 错误响应格式

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "输入数据验证失败",
    "details": {
      "field": "username",
      "issue": "用户名已存在"
    }
  }
}
```

## 数据模型

### User（用户）

```json
{
  "id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "full_name": "系统管理员",
  "is_active": true,
  "created_at": "2025-01-01T00:00:00",
  "updated_at": "2025-01-01T00:00:00",
  "roles": [...]
}
```

### Role（角色）

```json
{
  "id": 1,
  "name": "超级管理员",
  "description": "拥有系统所有权限的管理员角色",
  "created_at": "2025-01-01T00:00:00",
  "updated_at": "2025-01-01T00:00:00",
  "permissions": [...]
}
```

### Permission（权限）

```json
{
  "id": 1,
  "name": "用户查看",
  "resource": "users",
  "action": "read",
  "description": "查看用户信息",
  "created_at": "2025-01-01T00:00:00",
  "updated_at": "2025-01-01T00:00:00"
}
```

## 在线文档

启动后端服务后，可以访问以下地址查看交互式 API 文档：

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

这些文档提供了完整的 API 接口说明和在线测试功能。
