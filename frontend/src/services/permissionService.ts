/**
 * 权限管理服务
 */
import api from './api';
import { type Permission, type CreatePermissionRequest, type UpdatePermissionRequest, type PaginatedResponse, type PaginationParams } from '../types';

export const permissionService = {
  // 获取权限列表
  async getPermissions(params: PaginationParams = {}): Promise<PaginatedResponse<Permission>> {
    const { page = 1, size = 10, search } = params;
    const queryParams = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
    });
    
    if (search) {
      queryParams.append('search', search);
    }

    const response = await api.get(`/permissions?${queryParams}`);
    return {
      items: response.data.permissions,
      total: response.data.total,
      page: response.data.page,
      size: response.data.size,
      pages: response.data.pages,
    };
  },

  // 获取权限详情
  async getPermission(id: number): Promise<Permission> {
    const response = await api.get(`/permissions/${id}`);
    return response.data;
  },

  // 创建权限
  async createPermission(permissionData: CreatePermissionRequest): Promise<Permission> {
    const response = await api.post('/permissions', permissionData);
    return response.data;
  },

  // 更新权限
  async updatePermission(id: number, permissionData: UpdatePermissionRequest): Promise<Permission> {
    const response = await api.put(`/permissions/${id}`, permissionData);
    return response.data;
  },

  // 删除权限
  async deletePermission(id: number): Promise<void> {
    await api.delete(`/permissions/${id}`);
  },

  // 根据资源获取权限
  async getPermissionsByResource(resource: string): Promise<Permission[]> {
    const response = await api.get(`/permissions/resource/${resource}`);
    return response.data;
  },
};
