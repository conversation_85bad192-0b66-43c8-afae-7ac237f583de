/**
 * 角色管理服务
 */
import api from './api';
import { type Role, type CreateRoleRequest, type UpdateRoleRequest, type PaginatedResponse, type PaginationParams } from '../types';

export const roleService = {
  // 获取角色列表
  async getRoles(params: PaginationParams = {}): Promise<PaginatedResponse<Role>> {
    const { page = 1, size = 10, search } = params;
    const queryParams = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
    });
    
    if (search) {
      queryParams.append('search', search);
    }

    const response = await api.get(`/roles?${queryParams}`);
    return {
      items: response.data.roles,
      total: response.data.total,
      page: response.data.page,
      size: response.data.size,
      pages: response.data.pages,
    };
  },

  // 获取角色详情
  async getRole(id: number): Promise<Role> {
    const response = await api.get(`/roles/${id}`);
    return response.data;
  },

  // 创建角色
  async createRole(roleData: CreateRoleRequest): Promise<Role> {
    const response = await api.post('/roles', roleData);
    return response.data;
  },

  // 更新角色
  async updateRole(id: number, roleData: UpdateRoleRequest): Promise<Role> {
    const response = await api.put(`/roles/${id}`, roleData);
    return response.data;
  },

  // 删除角色
  async deleteRole(id: number): Promise<void> {
    await api.delete(`/roles/${id}`);
  },

  // 为角色分配权限
  async assignPermissions(roleId: number, permissionIds: number[]): Promise<Role> {
    const response = await api.post(`/roles/${roleId}/permissions`, { permission_ids: permissionIds });
    return response.data;
  },

  // 移除角色权限
  async removePermission(roleId: number, permissionId: number): Promise<Role> {
    const response = await api.delete(`/roles/${roleId}/permissions/${permissionId}`);
    return response.data;
  },
};
