/**
 * 用户管理服务
 */
import api from './api';
import { type User, type CreateUserRequest, type UpdateUserRequest, type PaginatedResponse, type PaginationParams } from '../types';

export const userService = {
  // 获取用户列表
  async getUsers(params: PaginationParams = {}): Promise<PaginatedResponse<User>> {
    const { page = 1, size = 10, search } = params;
    const queryParams = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
    });
    
    if (search) {
      queryParams.append('search', search);
    }

    const response = await api.get(`/users?${queryParams}`);
    return {
      items: response.data.users,
      total: response.data.total,
      page: response.data.page,
      size: response.data.size,
      pages: response.data.pages,
    };
  },

  // 获取用户详情
  async getUser(id: number): Promise<User> {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },

  // 创建用户
  async createUser(userData: CreateUserRequest): Promise<User> {
    const response = await api.post('/users', userData);
    return response.data;
  },

  // 更新用户
  async updateUser(id: number, userData: UpdateUserRequest): Promise<User> {
    const response = await api.put(`/users/${id}`, userData);
    return response.data;
  },

  // 删除用户
  async deleteUser(id: number): Promise<void> {
    await api.delete(`/users/${id}`);
  },

  // 为用户分配角色
  async assignRoles(userId: number, roleIds: number[]): Promise<User> {
    const response = await api.post(`/users/${userId}/roles`, { role_ids: roleIds });
    return response.data;
  },

  // 移除用户角色
  async removeRole(userId: number, roleId: number): Promise<User> {
    const response = await api.delete(`/users/${userId}/roles/${roleId}`);
    return response.data;
  },
};
