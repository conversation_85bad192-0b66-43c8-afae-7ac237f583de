/**
 * 用户管理页面
 */
import React, { useState, useEffect } from 'react';
import {
  Container,
  Header,
  Table,
  Button,
  SpaceBetween,
  Box,
  TextFilter,
  Pagination,
  Modal,
  Form,
  FormField,
  Input,
  Select,
  Alert,
  Badge,
} from '@cloudscape-design/components';
import { type User, type CreateUserRequest } from '../types';
import { userService } from '../services/userService';
import { useAuth } from '../contexts/AuthContext';

const UsersPage: React.FC = () => {
  const { hasPermission } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedItems, setSelectedItems] = useState<User[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [filterText, setFilterText] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [newUser, setNewUser] = useState<CreateUserRequest>({
    username: '',
    email: '',
    full_name: '',
    password: '',
    is_active: true,
  });

  const pageSize = 10;

  // 加载用户列表
  const loadUsers = async (page = 1, search = '') => {
    setLoading(true);
    try {
      const result = await userService.getUsers({
        page,
        size: pageSize,
        search: search || undefined,
      });
      setUsers(result.items);
      setTotalPages(result.pages);
      setTotalItems(result.total);
      setCurrentPage(page);
    } catch (err: any) {
      setError(err.error?.message || '加载用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  // 搜索处理
  const handleSearch = (value: string) => {
    setFilterText(value);
    setCurrentPage(1);
    loadUsers(1, value);
  };

  // 分页处理
  const handlePageChange = (page: number) => {
    loadUsers(page, filterText);
  };

  // 创建用户
  const handleCreateUser = async () => {
    setCreateLoading(true);
    setError('');
    try {
      await userService.createUser(newUser);
      setSuccess('用户创建成功');
      setShowCreateModal(false);
      setNewUser({
        username: '',
        email: '',
        full_name: '',
        password: '',
        is_active: true,
      });
      loadUsers(currentPage, filterText);
    } catch (err: any) {
      setError(err.error?.message || '创建用户失败');
    } finally {
      setCreateLoading(false);
    }
  };

  // 删除用户
  const handleDeleteUser = async (userId: number) => {
    if (!window.confirm('确定要删除这个用户吗？')) return;

    try {
      await userService.deleteUser(userId);
      setSuccess('用户删除成功');
      loadUsers(currentPage, filterText);
    } catch (err: any) {
      setError(err.error?.message || '删除用户失败');
    }
  };

  const columnDefinitions = [
    {
      id: 'username',
      header: '用户名',
      cell: (item: User) => item.username,
      sortingField: 'username',
    },
    {
      id: 'email',
      header: '邮箱',
      cell: (item: User) => item.email,
    },
    {
      id: 'full_name',
      header: '姓名',
      cell: (item: User) => item.full_name,
    },
    {
      id: 'is_active',
      header: '状态',
      cell: (item: User) => (
        <Badge color={item.is_active ? 'green' : 'red'}>
          {item.is_active ? '活跃' : '禁用'}
        </Badge>
      ),
    },
    {
      id: 'roles',
      header: '角色',
      cell: (item: User) => (
        <SpaceBetween direction="horizontal" size="xs">
          {item.roles.map((role) => (
            <Badge key={role.id}>{role.name}</Badge>
          ))}
        </SpaceBetween>
      ),
    },
    {
      id: 'created_at',
      header: '创建时间',
      cell: (item: User) => new Date(item.created_at).toLocaleString(),
    },
    {
      id: 'actions',
      header: '操作',
      cell: (item: User) => (
        <SpaceBetween direction="horizontal" size="xs">
          {hasPermission('users', 'update') && (
            <Button size="small">编辑</Button>
          )}
          {hasPermission('users', 'delete') && (
            <Button
              size="small"
              variant="link"
              onClick={() => handleDeleteUser(item.id)}
            >
              删除
            </Button>
          )}
        </SpaceBetween>
      ),
    },
  ];

  return (
    <Container>
      <SpaceBetween size="l">
        {error && (
          <Alert type="error" dismissible onDismiss={() => setError('')}>
            {error}
          </Alert>
        )}
        {success && (
          <Alert type="success" dismissible onDismiss={() => setSuccess('')}>
            {success}
          </Alert>
        )}

        <Table
          columnDefinitions={columnDefinitions}
          items={users}
          loading={loading}
          selectedItems={selectedItems}
          onSelectionChange={({ detail }) => setSelectedItems(detail.selectedItems)}
          selectionType="multi"
          header={
            <Header
              variant="h1"
              counter={`(${totalItems})`}
              actions={
                <SpaceBetween direction="horizontal" size="xs">
                  {hasPermission('users', 'create') && (
                    <Button
                      variant="primary"
                      onClick={() => setShowCreateModal(true)}
                    >
                      创建用户
                    </Button>
                  )}
                </SpaceBetween>
              }
            >
              用户管理
            </Header>
          }
          filter={
            <TextFilter
              filteringText={filterText}
              onChange={({ detail }) => handleSearch(detail.filteringText)}
              placeholder="搜索用户名、邮箱或姓名"
            />
          }
          pagination={
            <Pagination
              currentPageIndex={currentPage}
              pagesCount={totalPages}
              onChange={({ detail }) => handlePageChange(detail.currentPageIndex)}
            />
          }
          empty={
            <Box textAlign="center" color="inherit">
              <b>暂无用户</b>
              <Box padding={{ bottom: 's' }} variant="p" color="inherit">
                暂时没有用户数据。
              </Box>
              {hasPermission('users', 'create') && (
                <Button onClick={() => setShowCreateModal(true)}>
                  创建用户
                </Button>
              )}
            </Box>
          }
        />

        {/* 创建用户模态框 */}
        <Modal
          visible={showCreateModal}
          onDismiss={() => setShowCreateModal(false)}
          header="创建新用户"
          footer={
            <Box float="right">
              <SpaceBetween direction="horizontal" size="xs">
                <Button onClick={() => setShowCreateModal(false)}>
                  取消
                </Button>
                <Button
                  variant="primary"
                  loading={createLoading}
                  onClick={handleCreateUser}
                >
                  创建
                </Button>
              </SpaceBetween>
            </Box>
          }
        >
          <Form>
            <SpaceBetween size="l">
              <FormField label="用户名" constraintText="3-50 字符，唯一">
                <Input
                  value={newUser.username}
                  onChange={({ detail }) =>
                    setNewUser({ ...newUser, username: detail.value })
                  }
                  placeholder="请输入用户名"
                />
              </FormField>
              <FormField label="邮箱" constraintText="有效的邮箱地址">
                <Input
                  value={newUser.email}
                  onChange={({ detail }) =>
                    setNewUser({ ...newUser, email: detail.value })
                  }
                  placeholder="请输入邮箱"
                  type="email"
                />
              </FormField>
              <FormField label="姓名">
                <Input
                  value={newUser.full_name}
                  onChange={({ detail }) =>
                    setNewUser({ ...newUser, full_name: detail.value })
                  }
                  placeholder="请输入姓名"
                />
              </FormField>
              <FormField label="密码" constraintText="至少 6 个字符">
                <Input
                  value={newUser.password}
                  onChange={({ detail }) =>
                    setNewUser({ ...newUser, password: detail.value })
                  }
                  placeholder="请输入密码"
                  type="password"
                />
              </FormField>
              <FormField label="状态">
                <Select
                  selectedOption={{
                    label: newUser.is_active ? '活跃' : '禁用',
                    value: newUser.is_active.toString(),
                  }}
                  onChange={({ detail }) =>
                    setNewUser({
                      ...newUser,
                      is_active: detail.selectedOption.value === 'true',
                    })
                  }
                  options={[
                    { label: '活跃', value: 'true' },
                    { label: '禁用', value: 'false' },
                  ]}
                />
              </FormField>
            </SpaceBetween>
          </Form>
        </Modal>
      </SpaceBetween>
    </Container>
  );
};

export default UsersPage;
